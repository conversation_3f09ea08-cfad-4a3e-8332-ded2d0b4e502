﻿  Compiling CUDA source file src\Matting_Kernels.cu...
  Compiling CUDA source file src\main_Kernels.cu...
  
  
  F:\Catechese\EditeurAudioVideo\ImageMatter>"C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.9\bin\nvcc.exe" -gencode=arch=compute_52,code=\"sm_52,compute_52\" --use-local-env -ccbin "C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\bin\HostX64\x64" -x cu   -I"C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.9\\include" -IF:\VccLibs\ffmpeg\include -IF:\VccLibs\onnxruntime\include -Iinclude -IF:\VccLibs\TensorRT\include -I"C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.9\include" -I"C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.9\include"  -G   --keep-dir ImageMatter\x64\Debug  -maxrregcount=0    --machine 64 --compile -cudart static --expt-relaxed-constexpr -g  -DWIN32 -DWIN64 -D_DEBUG -D_CONSOLE -D_MBCS -Xcompiler "/EHsc /W3 /nologo /Od /FS /Zi /RTC1 /MDd " -Xcompiler "/FdImageMatter\x64\Debug\vc143.pdb" -o F:\Catechese\EditeurAudioVideo\ImageMatter\ImageMatter\x64\Debug\Matting_Kernels.cu.obj "F:\Catechese\EditeurAudioVideo\ImageMatter\src\Matting_Kernels.cu" 
  F:\Catechese\EditeurAudioVideo\ImageMatter>"C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.9\bin\nvcc.exe" -gencode=arch=compute_52,code=\"sm_52,compute_52\" --use-local-env -ccbin "C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\bin\HostX64\x64" -x cu   -I"C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.9\\include" -IF:\VccLibs\ffmpeg\include -IF:\VccLibs\onnxruntime\include -Iinclude -IF:\VccLibs\TensorRT\include -I"C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.9\include" -I"C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.9\include"  -G   --keep-dir ImageMatter\x64\Debug  -maxrregcount=0    --machine 64 --compile -cudart static --expt-relaxed-constexpr -g  -DWIN32 -DWIN64 -D_DEBUG -D_CONSOLE -D_MBCS -Xcompiler "/EHsc /W3 /nologo /Od /FS /Zi /RTC1 /MDd " -Xcompiler "/FdImageMatter\x64\Debug\vc143.pdb" -o F:\Catechese\EditeurAudioVideo\ImageMatter\ImageMatter\x64\Debug\main_Kernels.cu.obj "F:\Catechese\EditeurAudioVideo\ImageMatter\src\main_Kernels.cu" 
CUDACOMPILE : nvcc warning : Support for offline compilation for architectures prior to '<compute/sm/lto>_75' will be removed in a future release (Use -Wno-deprecated-gpu-targets to suppress warning).
  main_Kernels.cu
  tmpxft_00007c3c_00000000-7_main_Kernels.cudafe1.cpp
CUDACOMPILE : nvcc warning : Support for offline compilation for architectures prior to '<compute/sm/lto>_75' will be removed in a future release (Use -Wno-deprecated-gpu-targets to suppress warning).
  Matting_Kernels.cu
  tmpxft_00006b84_00000000-7_Matting_Kernels.cudafe1.cpp
  DirectVideoReader.cpp
  DirectVideoReaderAlpha.cpp
  FrameProcessor.cpp
  HeadDetector.cpp
  Helpers.cpp
  Helpers_Heads.cpp
  ImageMatting.cpp
  ImageMattingFactory.cpp
  ImageMattingOnnx.cpp
  ImageMattingTensorRt.cpp
  VideoWriterAlpha.cpp
  main.cpp
  Génération de code en cours...
  ImageMatter.vcxproj -> F:\Catechese\EditeurAudioVideo\ImageMatter\x64\Debug\ImageMatter.exe
  0 fichier(s) copiÚ(s)
  0 fichier(s) copiÚ(s)
  0 fichier(s) copiÚ(s)
  0 fichier(s) copiÚ(s)
  0 fichier(s) copiÚ(s)
  ModelsMatting\ben2_fp16.onnx
  1 fichier(s) copiÚ(s)
