﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="4.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup>
    <CudaCompile Include="src\main_Kernels.cu" />
    <CudaCompile Include="src\CudaProResKernels.cu" />
    <CudaCompile Include="src\Matting_Kernels.cu" />
    <CudaCompile Include="src\Helpers_Kernels.cu" />
    <CudaCompile Include="src\DownscaleResizingKernels.cu" />
    <CudaCompile Include="src\UpscaleResizingKernels.cu" />
    <CudaCompile Include="src\HeadDetectorKernels.cu" />
    <CudaCompile Include="src\BackgroundEstimationKernels.cu" />
    <CudaCompile Include="src\alpha_codec.cu" />
  </ItemGroup>
  <ItemGroup>
    <ClCompile Include="src\DirectVideoWriter.cpp" />
    <ClCompile Include="src\VideoWriterAlpha.cpp" />
    <ClCompile Include="src\CudaProResEncoder.cpp" />
    <ClCompile Include="src\Helpers.cpp" />
    <ClCompile Include="src\lodepng.cpp" />
    <ClCompile Include="src\DirectVideoReader.cpp" />
    <ClCompile Include="src\DirectVideoReaderAlpha.cpp" />
    <ClCompile Include="src\HeadDetector.cpp" />
    <ClCompile Include="src\ImageMattingOnnx.cpp" />
    <ClCompile Include="src\main.cpp" />
    <ClCompile Include="src\Helpers_Heads.cpp" />
    <ClCompile Include="src\FrameProcessor.cpp" />
    <ClCompile Include="src\ImageMattingTensorRt.cpp" />
    <ClCompile Include="src\DirectVideoWriterAlphaCuda.cpp" />
    <ClCompile Include="src\ImageMatting.cpp" />
    <ClCompile Include="src\ImageMattingFactory.cpp" />
    <ClCompile Include="src\alpha_codec.cpp" />
    <ClCompile Include="src\main_twophase.cpp" />
    <ClCompile Include="src\VideoBackgroundRemoval.cpp" />
    <ClCompile Include="src\main_onephase.cpp" />
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="include\HeadDetection_Onnx.h" />
    <ClInclude Include="include\DirectVideoWriter.h" />
    <ClInclude Include="include\VideoWriterAlpha.h" />
    <ClInclude Include="include\CudaProResEncoder.h" />
    <ClInclude Include="include\Helpers.h" />
    <ClInclude Include="include\lodepng.h" />
    <ClInclude Include="include\main_Kernels.cuh" />
    <ClInclude Include="include\DirectVideoReader.h" />
    <ClInclude Include="include\DirectVideoReaderAlpha.h" />
    <ClInclude Include="include\framework.h" />
    <ClInclude Include="include\HeadDetector.h" />
    <ClInclude Include="include\ImageMattingOnnx.h" />
    <ClInclude Include="include\Matting_Kernels.cuh" />
    <ClInclude Include="include\StringUtils.h" />
    <ClInclude Include="include\Helpers_Heads.h" />
    <ClInclude Include="include\Helpers_Kernels.cuh" />
    <ClInclude Include="include\FrameProcessor.h" />
    <ClInclude Include="include\DownscaleResizingKernels.cuh" />
    <ClInclude Include="include\UpscaleResizingKernels.cuh" />
    <ClInclude Include="include\HeadDetectorKernels.cuh" />
    <ClInclude Include="include\AlphaVideoExample.h" />
    <ClInclude Include="include\ImageMattingTensorRt.h" />
    <ClInclude Include="include\DirectVideoWriterAlphaCuda.h" />
    <ClInclude Include="include\ImageMatting.h" />
    <ClInclude Include="include\ImageMattingFactory.h" />
    <ClInclude Include="include\BackgroundEstimationKernels.cuh" />
    <ClInclude Include="include\alpha_codec.h" />
    <ClInclude Include="include\alpha_codec.cuh" />
    <ClInclude Include="include\VideoBackgroundRemoval.h" />
  </ItemGroup>
  <ItemGroup>
    <None Include=".clang-format" />
  </ItemGroup>
  <ItemGroup>
    <Text Include="src\IMPORTANT - Principles.txt" />
  </ItemGroup>
</Project>