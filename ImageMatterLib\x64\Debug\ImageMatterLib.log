﻿  ImageMatterLib.cpp
  VideoBackgroundRemoval.cpp
  FrameProcessor.cpp
  DirectVideoReader.cpp
  DirectVideoWriterAlphaCuda.cpp
  ImageMattingFactory.cpp
  ImageMatting.cpp
  ImageMattingOnnx.cpp
  ImageMattingTensorRt.cpp
  HeadDetector.cpp
  Helpers.cpp
  Helpers_Heads.cpp
  alpha_codec.cpp
  CudaProResEncoder.cpp
  lodepng.cpp
  DirectVideoWriter.cpp
  VideoWriterAlpha.cpp
  Génération de code en cours...
     Création de la bibliothèque F:\Catechese\EditeurAudioVideo\ImageMatter\x64\Debug\ImageMatterLib.lib et de l'objet F:\Catechese\EditeurAudioVideo\ImageMatter\x64\Debug\ImageMatterLib.exp
  ImageMatterLib.vcxproj -> F:\Catechese\EditeurAudioVideo\ImageMatter\x64\Debug\ImageMatterLib.dll
