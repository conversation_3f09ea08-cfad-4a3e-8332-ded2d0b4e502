# ImageMatterLib

A C++ DLL for video background removal and ProRes 4444 export, designed for use from C# and other languages via P/Invoke.

## Features
- Converts a video to ProRes 4444 with alpha (background removed)
- Supports multiple AI engine types (ONNX, TensorRT, Auto)
- Supports one-step or two-step extraction
- Progress callback for frame updates and cancellation

## Exported Function
```
extern "C" __declspec(dllexport) int ConvertVideoToProRes4444(
    const wchar_t* inputVideo,
    const wchar_t* outputVideo,
    EngineType engine,
    bool twoStep,
    ProgressCallback progressCb,
    void* userData
);
```

- Returns 0 on success, nonzero on error or cancellation.
- `EngineType` is an enum: `ENGINE_ONNX = 0`, `ENGINE_TENSORRT = 1`, `ENGINE_AUTO = 2`.
- `ProgressCallback` is called for each frame. Return `false` to cancel.

## C# Usage Example
```csharp
using System;
using System.Runtime.InteropServices;

public enum EngineType
{
    Onnx = 0,
    TensorRt = 1,
    Auto = 2
}

[UnmanagedFunctionPointer(CallingConvention.StdCall)]
public delegate bool ProgressCallback(int currentFrame, int totalFrames, IntPtr userData);

public static class ImageMatterLib
{
    [DllImport("ImageMatterLib.dll", CharSet = CharSet.Unicode)]
    public static extern int ConvertVideoToProRes4444(
        string inputVideo,
        string outputVideo,
        EngineType engine,
        bool twoStep,
        ProgressCallback progressCb,
        IntPtr userData
    );
}

// Example usage:
class Program
{
    static bool Progress(int current, int total, IntPtr userData)
    {
        Console.WriteLine($"Frame {current + 1} / {total}");
        // Return false to cancel
        return true;
    }

    static void Main()
    {
        int result = ImageMatterLib.ConvertVideoToProRes4444(
            "input.mp4",
            "output.mov",
            EngineType.Auto,
            false, // one-step
            Progress,
            IntPtr.Zero
        );
        Console.WriteLine(result == 0 ? "Success" : $"Error: {result}");
    }
}
