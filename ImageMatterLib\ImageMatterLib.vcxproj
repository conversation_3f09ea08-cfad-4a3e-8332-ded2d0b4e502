<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup Label="ProjectConfigurations">
    <ProjectConfiguration Include="Debug|x64">
      <Configuration>Debug</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|x64">
      <Configuration>Release</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
  </ItemGroup>
  <PropertyGroup Label="Globals">
    <ProjectGuid>{A1B2C3D4-E5F6-7890-1234-56789ABCDEF0}</ProjectGuid>
    <Keyword>Win32Proj</Keyword>
    <RootNamespace>ImageMatterLib</RootNamespace>
    <WindowsTargetPlatformVersion>10.0</WindowsTargetPlatformVersion>
    <ConfigurationType>DynamicLibrary</ConfigurationType>
    <PlatformToolset>v143</PlatformToolset>
    <TargetName>ImageMatterLib</TargetName>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.Default.props" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'" Label="Configuration">
    <ConfigurationType>DynamicLibrary</ConfigurationType>
    <UseDebugLibraries>true</UseDebugLibraries>
    <PlatformToolset>v143</PlatformToolset>
    <CharacterSet>Unicode</CharacterSet>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'" Label="Configuration">
    <ConfigurationType>DynamicLibrary</ConfigurationType>
    <UseDebugLibraries>false</UseDebugLibraries>
    <PlatformToolset>v143</PlatformToolset>
    <WholeProgramOptimization>true</WholeProgramOptimization>
    <CharacterSet>Unicode</CharacterSet>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.props" />
  <ImportGroup Label="ExtensionSettings">
    <Import Project="$(VCTargetsPath)\BuildCustomizations\CUDA 12.9.props" />
  </ImportGroup>
  <ImportGroup Label="Shared" />
  <ImportGroup Label="PropertySheets" Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" />
  </ImportGroup>
  <ImportGroup Label="PropertySheets" Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" />
  </ImportGroup>
  <PropertyGroup Label="UserMacros" />
  <PropertyGroup>
    <IncludePath>$(ProjectDir)..\include;$(ProjectDir)..\src;$(IncludePath)</IncludePath>
    <LibraryPath>$(ProjectDir)..\x64\$(Configuration);$(LibraryPath)</LibraryPath>
    <TargetExt>.dll</TargetExt>
  </PropertyGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <ClCompile>
      <PreprocessorDefinitions>IMAGEMATTERLIB_EXPORTS;_CRT_SECURE_NO_WARNINGS;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <RuntimeLibrary>MultiThreadedDebugDLL</RuntimeLibrary>
      <WarningLevel>Level3</WarningLevel>
      <Optimization>Disabled</Optimization>
      <AdditionalIncludeDirectories>$(CUDA_PATH)\include;$(VccLibs)\ffmpeg\include;$(VccLibs)\onnxruntime\include;$(VccLibs)\TensorRT\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <LanguageStandard>stdcpp17</LanguageStandard>
    </ClCompile>
    <Link>
      <SubSystem>Windows</SubSystem>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <AdditionalLibraryDirectories>$(CUDA_PATH)\lib\x64;$(VccLibs)\ffmpeg\bin;$(VccLibs)\onnxruntime\lib;$(VccLibs)\TensorRT\lib;%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <AdditionalDependencies>nvinfer_10.lib;nvonnxparser_10.lib;nvinfer_plugin_10.lib;cudart_static.lib;cuda.lib;cublas.lib;curand.lib;cusparse.lib;cusolver.lib;cufft.lib;avcodec.lib;avformat.lib;avutil.lib;swresample.lib;avfilter.lib;swscale.lib;onnxruntime.lib;kernel32.lib;user32.lib;gdi32.lib;winspool.lib;comdlg32.lib;advapi32.lib;shell32.lib;ole32.lib;oleaut32.lib;uuid.lib;odbc32.lib;odbccp32.lib;%(AdditionalDependencies)</AdditionalDependencies>
      <IgnoreSpecificDefaultLibraries>LIBCMT</IgnoreSpecificDefaultLibraries>
    </Link>
    <CudaCompile>
      <TargetMachinePlatform>64</TargetMachinePlatform>
      <AdditionalOptions>--expt-relaxed-constexpr %(AdditionalOptions)</AdditionalOptions>
    </CudaCompile>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <ClCompile>
      <PreprocessorDefinitions>IMAGEMATTERLIB_EXPORTS;_CRT_SECURE_NO_WARNINGS;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <RuntimeLibrary>MultiThreadedDLL</RuntimeLibrary>
      <WarningLevel>Level3</WarningLevel>
      <Optimization>MaxSpeed</Optimization>
      <FunctionLevelLinking>true</FunctionLevelLinking>
      <IntrinsicFunctions>true</IntrinsicFunctions>
      <AdditionalIncludeDirectories>$(CUDA_PATH)\include;$(VccLibs)\ffmpeg\include;$(VccLibs)\onnxruntime\include;$(VccLibs)\TensorRT\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <LanguageStandard>stdcpp17</LanguageStandard>
    </ClCompile>
    <Link>
      <SubSystem>Windows</SubSystem>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <EnableCOMDATFolding>true</EnableCOMDATFolding>
      <OptimizeReferences>true</OptimizeReferences>
      <AdditionalLibraryDirectories>$(CUDA_PATH)\lib\x64;$(VccLibs)\ffmpeg\bin;$(VccLibs)\onnxruntime\lib;C:\Program Files\NVIDIA GPU Computing Toolkit\TensorRT\lib;%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <AdditionalDependencies>nvinfer_10.lib;nvonnxparser_10.lib;nvinfer_plugin_10.lib;cudart_static.lib;cuda.lib;cublas.lib;curand.lib;cusparse.lib;cusolver.lib;cufft.lib;avcodec.lib;avformat.lib;avutil.lib;swresample.lib;avfilter.lib;swscale.lib;onnxruntime.lib;kernel32.lib;user32.lib;gdi32.lib;winspool.lib;comdlg32.lib;advapi32.lib;shell32.lib;ole32.lib;oleaut32.lib;uuid.lib;odbc32.lib;odbccp32.lib;%(AdditionalDependencies)</AdditionalDependencies>
      <IgnoreSpecificDefaultLibraries>LIBCMT</IgnoreSpecificDefaultLibraries>
    </Link>
    <CudaCompile>
      <TargetMachinePlatform>64</TargetMachinePlatform>
      <AdditionalOptions>--expt-relaxed-constexpr %(AdditionalOptions)</AdditionalOptions>
    </CudaCompile>
  </ItemDefinitionGroup>
  <ItemGroup>
    <ClInclude Include="ImageMatterLib.h" />
    <ClInclude Include="..\include\VideoBackgroundRemoval.h" />
    <ClInclude Include="..\include\FrameProcessor.h" />
    <ClInclude Include="..\include\DirectVideoReader.h" />
    <ClInclude Include="..\include\DirectVideoWriterAlphaCuda.h" />
    <ClInclude Include="..\include\ImageMattingFactory.h" />
    <ClInclude Include="..\include\ImageMatting.h" />
    <ClInclude Include="..\include\ImageMattingOnnx.h" />
    <ClInclude Include="..\include\ImageMattingTensorRt.h" />
    <ClInclude Include="..\include\HeadDetector.h" />
    <ClInclude Include="..\include\Helpers.h" />
    <ClInclude Include="..\include\Helpers_Heads.h" />
    <ClInclude Include="..\include\alpha_codec.h" />
    <ClInclude Include="..\include\CudaProResEncoder.h" />
    <ClInclude Include="..\include\lodepng.h" />
    <ClInclude Include="..\include\framework.h" />
    <ClInclude Include="..\include\StringUtils.h" />
    <ClInclude Include="..\include\DirectVideoWriter.h" />
    <ClInclude Include="..\include\VideoWriterAlpha.h" />
    <ClInclude Include="..\include\DirectVideoReaderAlpha.h" />
    <ClInclude Include="..\include\AlphaVideoExample.h" />
    <ClInclude Include="..\include\HeadDetection_Onnx.h" />
    <ClInclude Include="..\include\BackgroundEstimationKernels.cuh" />
    <ClInclude Include="..\include\HeadDetectorKernels.cuh" />
    <ClInclude Include="..\include\Helpers_Kernels.cuh" />
    <ClInclude Include="..\include\main_Kernels.cuh" />
    <ClInclude Include="..\include\Matting_Kernels.cuh" />
    <ClInclude Include="..\include\DownscaleResizingKernels.cuh" />
    <ClInclude Include="..\include\UpscaleResizingKernels.cuh" />
    <ClInclude Include="..\include\alpha_codec.cuh" />
  </ItemGroup>
  <ItemGroup>
    <ClCompile Include="ImageMatterLib.cpp" />
    <ClCompile Include="..\src\VideoBackgroundRemoval.cpp" />
    <ClCompile Include="..\src\FrameProcessor.cpp" />
    <ClCompile Include="..\src\DirectVideoReader.cpp" />
    <ClCompile Include="..\src\DirectVideoWriterAlphaCuda.cpp" />
    <ClCompile Include="..\src\ImageMattingFactory.cpp" />
    <ClCompile Include="..\src\ImageMatting.cpp" />
    <ClCompile Include="..\src\ImageMattingOnnx.cpp" />
    <ClCompile Include="..\src\ImageMattingTensorRt.cpp" />
    <ClCompile Include="..\src\HeadDetector.cpp" />
    <ClCompile Include="..\src\Helpers.cpp" />
    <ClCompile Include="..\src\Helpers_Heads.cpp" />
    <ClCompile Include="..\src\alpha_codec.cpp" />
    <ClCompile Include="..\src\CudaProResEncoder.cpp" />
    <ClCompile Include="..\src\lodepng.cpp" />
    <ClCompile Include="..\src\DirectVideoWriter.cpp" />
    <ClCompile Include="..\src\VideoWriterAlpha.cpp" />
    <ClCompile Include="..\src\DirectVideoReaderAlpha.cpp" />
  </ItemGroup>
  <ItemGroup>
    <CudaCompile Include="..\src\alpha_codec.cu" />
    <CudaCompile Include="..\src\BackgroundEstimationKernels.cu" />
    <CudaCompile Include="..\src\HeadDetectorKernels.cu" />
    <CudaCompile Include="..\src\Helpers_Kernels.cu" />
    <CudaCompile Include="..\src\main_Kernels.cu" />
    <CudaCompile Include="..\src\CudaProResKernels.cu" />
    <CudaCompile Include="..\src\Matting_Kernels.cu" />
    <CudaCompile Include="..\src\DownscaleResizingKernels.cu" />
    <CudaCompile Include="..\src\UpscaleResizingKernels.cu" />
  </ItemGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.targets" />
  <ImportGroup Label="ExtensionTargets">
    <Import Project="$(VCTargetsPath)\BuildCustomizations\CUDA 12.9.targets" />
  </ImportGroup>
</Project>