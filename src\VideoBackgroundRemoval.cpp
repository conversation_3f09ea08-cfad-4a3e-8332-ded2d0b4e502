#include "VideoBackgroundRemoval.h"
#include "FrameProcessor.h"
#include "DirectVideoReader.h"
#include "DirectVideoWriterAlphaCuda.h"
#include "ImageMattingFactory.h"
#include "Helpers.h"
#include <cuda.h>
#include <cuda_runtime.h>
#include <string>
#include <memory>
#include <atomic>
#include <functional>
#include <chrono>
#include <iostream>
#include <iomanip>
#include <vector>

extern "C" {
#include <libavutil/log.h>
}

// Structure to hold compressed alpha data for each frame (for two-phase processing)
struct FrameAlphaData {
    int frameIndex;
    unsigned char* encodedAlpha;
    size_t encodedSize;
    double timestamp;
};

// Helper function to convert wstring to string
std::string WStringToString(const std::wstring& wstr) {
    if (wstr.empty()) return std::string();
    int size_needed = WideCharToMultiByte(CP_UTF8, 0, &wstr[0], (int)wstr.size(), NULL, 0, NULL, NULL);
    std::string strTo(size_needed, 0);
    WideCharToMultiByte(CP_UTF8, 0, &wstr[0], (int)wstr.size(), &strTo[0], size_needed, NULL, NULL);
    return strTo;
}

// One-phase processing implementation
int ProcessOnePhase(
    const std::string& inputPath,
    const std::string& outputPath,
    CUcontext context,
    ProgressCallback progressCb,
    void* userData
) {
    try {
        // Create regular video reader (hardware accelerated for speed)
        auto reader = DirectVideoReader::Create(inputPath, context, true);
        if (!reader) {
            std::cerr << "Failed to create video reader for: " << inputPath << std::endl;
            return 1;
        }

        // Create CUDA stream for processing
        cudaStream_t processingStream;
        CUDA_CHECK(cudaStreamCreate(&processingStream));

        // Initialize frame processor
        auto processor = std::make_unique<FrameProcessor>();
        if (!processor->Initialize(reader->GetWidth(), reader->GetHeight(), processingStream)) {
            std::cerr << "Failed to initialize FrameProcessor" << std::endl;
            cudaStreamDestroy(processingStream);
            return 1;
        }

        // Configure output for CUDA ProRes 4444 with alpha
        DirectVideoWriterAlphaCuda::OutputConfig outputConfig;
        outputConfig.width = reader->GetWidth();
        outputConfig.height = reader->GetHeight();
        outputConfig.frameRate = reader->GetFrameRate();
        outputConfig.outputPath = outputPath;
        outputConfig.UseProRes4444();

        // Create CUDA alpha video writer
        auto writer = DirectVideoWriterAlphaCuda::Create(outputConfig, context);
        if (!writer) {
            std::cerr << "Failed to create CUDA ProRes video writer" << std::endl;
            processor->Cleanup();
            cudaStreamDestroy(processingStream);
            return 1;
        }

        // Allocate buffers for hardware-accelerated video reading (NV12 format)
        int nv12Height = reader->GetHeight() + reader->GetHeight() / 2;
        void* d_inputNv12Array = nullptr;
        size_t inputPitch = 0;
        CUDA_CHECK(cudaMallocPitch(&d_inputNv12Array, &inputPitch, reader->GetWidth(), nv12Height));

        // Allocate buffer for RGBA output
        size_t rgbaBufferSize = reader->GetWidth() * reader->GetHeight() * 4 * sizeof(float);
        void* d_rgbaBuffer = nullptr;
        CUDA_CHECK(cudaMalloc(&d_rgbaBuffer, rgbaBufferSize));

        int frameCount = 0;
        double timestamp = 0.0;
        auto startTime = std::chrono::high_resolution_clock::now();

        // Process all frames
        while (true) {
            // Read frame
            timestamp = reader->ReadFrame(d_inputNv12Array, inputPitch * nv12Height, inputPitch);
            if (timestamp < 0.0) break;

            // Process frame with FrameProcessor (AI Alpha Matting + Background Estimation)
            if (!processor->ProcessFrame(d_inputNv12Array, inputPitch, static_cast<float*>(d_rgbaBuffer))) {
                std::cerr << "Failed to process frame " << frameCount << " with FrameProcessor" << std::endl;
                break;
            }

            // Write frame to ProRes
            if (!writer->WriteFrame(d_rgbaBuffer, rgbaBufferSize)) {
                std::cerr << "Failed to write frame " << frameCount << std::endl;
                break;
            }

            frameCount++;

            // Call progress callback if provided
            if (progressCb) {
                if (!progressCb(frameCount, 0, userData)) {
                    // User requested cancellation
                    break;
                }
            }
        }

        // Finalize video
        writer->Finalize();

        // Clean up
        processor->Cleanup();
        if (d_inputNv12Array) cudaFree(d_inputNv12Array);
        if (d_rgbaBuffer) cudaFree(d_rgbaBuffer);
        if (processingStream) cudaStreamDestroy(processingStream);

        auto endTime = std::chrono::high_resolution_clock::now();
        auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(endTime - startTime).count() / 1000.0;

        std::cout << "One-phase processing completed: " << frameCount << " frames processed in "
                 << std::fixed << std::setprecision(2) << duration << " seconds" << std::endl;

        return 0; // Success
    }
    catch (const std::exception& e) {
        std::cerr << "Error in one-phase processing: " << e.what() << std::endl;
        return 1;
    }
}

// Two-phase processing implementation
int ProcessTwoPhase(
    const std::string& inputPath,
    const std::string& outputPath,
    CUcontext context,
    ProgressCallback progressCb,
    void* userData
) {
    try {
        // Create regular video reader (hardware accelerated for speed)
        auto reader = DirectVideoReader::Create(inputPath, context, true);
        if (!reader) {
            std::cerr << "Failed to create video reader for: " << inputPath << std::endl;
            return 1;
        }

        // Create CUDA stream for processing
        cudaStream_t processingStream;
        CUDA_CHECK(cudaStreamCreate(&processingStream));

        // Allocate buffers for hardware-accelerated video reading (NV12 format)
        int nv12Height = reader->GetHeight() + reader->GetHeight() / 2;
        void* d_inputNv12Array = nullptr;
        size_t inputPitch = 0;
        CUDA_CHECK(cudaMallocPitch(&d_inputNv12Array, &inputPitch, reader->GetWidth(), nv12Height));

        // Vector to store compressed alpha data for all frames
        std::vector<FrameAlphaData> frameAlphaDataList;

        // ===== PHASE 1: Initial Alpha Generation =====
        std::cout << "Phase 1: Initial Alpha Generation" << std::endl;

        // Initialize frame processor for initial matting only
        auto processor = std::make_unique<FrameProcessor>();
        if (!processor->InitializeInitialMatte(reader->GetWidth(), reader->GetHeight(), processingStream)) {
            std::cerr << "Failed to initialize FrameProcessor for initial matting" << std::endl;
            cudaStreamDestroy(processingStream);
            if (d_inputNv12Array) cudaFree(d_inputNv12Array);
            return 1;
        }

        // Timing for phase 1
        int frameCount = 0;
        double timestamp = 0.0;
        auto phase1StartTime = std::chrono::high_resolution_clock::now();

        // Process all frames for initial alpha generation
        while (true) {
            // Read frame
            timestamp = reader->ReadFrame(d_inputNv12Array, inputPitch * nv12Height, inputPitch);
            if (timestamp < 0.0) break;

            // Generate initial alpha and compress it
            unsigned char* encodedAlpha = nullptr;
            size_t encodedSize = 0;
            if (!processor->FindInitialAlpha(d_inputNv12Array, inputPitch, &encodedAlpha, &encodedSize)) {
                std::cerr << "Failed to generate initial alpha for frame " << frameCount << std::endl;
                break;
            }

            // Store compressed alpha data
            FrameAlphaData frameData;
            frameData.frameIndex = frameCount;
            frameData.encodedAlpha = encodedAlpha;
            frameData.encodedSize = encodedSize;
            frameData.timestamp = timestamp;
            frameAlphaDataList.push_back(frameData);

            frameCount++;

            // Call progress callback if provided (phase 1 is first half of progress)
            if (progressCb) {
                if (!progressCb(frameCount, frameCount * 2, userData)) {
                    // User requested cancellation
                    break;
                }
            }
        }

        auto phase1EndTime = std::chrono::high_resolution_clock::now();
        double phase1Duration = std::chrono::duration<double>(phase1EndTime - phase1StartTime).count();

        std::cout << "Phase 1 completed: " << frameCount << " frames processed in "
                 << std::fixed << std::setprecision(2) << phase1Duration << " seconds" << std::endl;

        // Clean up initial matting processor to free GPU memory
        processor->Cleanup();
        processor.reset();

        // ===== PHASE 2: Alpha Refinement and Final Processing =====
        std::cout << "Phase 2: Alpha Refinement and Final Processing" << std::endl;

        // Initialize frame processor for refinement
        processor = std::make_unique<FrameProcessor>();
        if (!processor->InitializeRefinement(reader->GetWidth(), reader->GetHeight(), processingStream)) {
            std::cerr << "Failed to initialize FrameProcessor for refinement" << std::endl;
            // Clean up alpha data
            for (auto& frameData : frameAlphaDataList) {
                if (frameData.encodedAlpha) free(frameData.encodedAlpha);
            }
            cudaStreamDestroy(processingStream);
            if (d_inputNv12Array) cudaFree(d_inputNv12Array);
            return 1;
        }

        // Configure output for CUDA ProRes 4444 with alpha
        DirectVideoWriterAlphaCuda::OutputConfig outputConfig;
        outputConfig.width = reader->GetWidth();
        outputConfig.height = reader->GetHeight();
        outputConfig.frameRate = reader->GetFrameRate();
        outputConfig.outputPath = outputPath;
        outputConfig.UseProRes4444();

        // Create CUDA alpha video writer
        auto writer = DirectVideoWriterAlphaCuda::Create(outputConfig, context);
        if (!writer) {
            std::cerr << "Failed to create CUDA ProRes video writer" << std::endl;
            processor->Cleanup();
            // Clean up alpha data
            for (auto& frameData : frameAlphaDataList) {
                if (frameData.encodedAlpha) free(frameData.encodedAlpha);
            }
            cudaStreamDestroy(processingStream);
            if (d_inputNv12Array) cudaFree(d_inputNv12Array);
            return 1;
        }

        // Allocate buffer for RGBA output
        size_t rgbaBufferSize = reader->GetWidth() * reader->GetHeight() * 4 * sizeof(float);
        void* d_rgbaBuffer = nullptr;
        CUDA_CHECK(cudaMalloc(&d_rgbaBuffer, rgbaBufferSize));

        // Reset reader for second pass
        reader.reset();
        reader = DirectVideoReader::Create(inputPath, context, true);

        // Timing for phase 2
        auto phase2StartTime = std::chrono::high_resolution_clock::now();

        // Process all frames for refinement and encoding
        int currentFrameIndex = 0;
        while (true) {
            // Read frame
            timestamp = reader->ReadFrame(d_inputNv12Array, inputPitch * nv12Height, inputPitch);
            if (timestamp < 0.0) break;

            // Get corresponding alpha data
            if (currentFrameIndex >= frameAlphaDataList.size()) {
                std::cerr << "Frame index mismatch in phase 2" << std::endl;
                break;
            }

            FrameAlphaData& frameData = frameAlphaDataList[currentFrameIndex];

            // Refine alpha and generate final RGBA
            if (!processor->RefineInitialAlpha(d_inputNv12Array, inputPitch,
                                             frameData.encodedAlpha, frameData.encodedSize,
                                             static_cast<float*>(d_rgbaBuffer))) {
                std::cerr << "Failed to refine alpha for frame " << currentFrameIndex << std::endl;
                break;
            }

            // Encode frame
            if (!writer->WriteFrame(d_rgbaBuffer, rgbaBufferSize)) {
                std::cerr << "Failed to write frame " << currentFrameIndex << std::endl;
                break;
            }

            currentFrameIndex++;

            // Call progress callback if provided (phase 2 is second half of progress)
            if (progressCb) {
                if (!progressCb(frameCount + currentFrameIndex, frameCount * 2, userData)) {
                    // User requested cancellation
                    break;
                }
            }
        }

        auto phase2EndTime = std::chrono::high_resolution_clock::now();
        double phase2Duration = std::chrono::duration<double>(phase2EndTime - phase2StartTime).count();

        std::cout << "Phase 2 completed: " << currentFrameIndex << " frames processed in "
                 << std::fixed << std::setprecision(2) << phase2Duration << " seconds" << std::endl;

        // Finalize video
        writer->Finalize();

        // Clean up frame processor
        processor->Cleanup();

        // Clean up compressed alpha data
        for (auto& frameData : frameAlphaDataList) {
            if (frameData.encodedAlpha) {
                free(frameData.encodedAlpha);
            }
        }

        // Cleanup buffers
        if (d_inputNv12Array) cudaFree(d_inputNv12Array);
        if (d_rgbaBuffer) cudaFree(d_rgbaBuffer);
        if (processingStream) cudaStreamDestroy(processingStream);

        // Final timing summary
        double totalDuration = phase1Duration + phase2Duration;
        std::cout << "Two-phase processing completed: " << frameCount << " frames processed in "
                 << std::fixed << std::setprecision(2) << totalDuration << " seconds" << std::endl;

        return 0; // Success
    }
    catch (const std::exception& e) {
        std::cerr << "Error in two-phase processing: " << e.what() << std::endl;
        return 1;
    }
}

// Main function: Returns 0 on success, nonzero on error/cancel
int VideoBackgroundRemoval(
    const std::wstring& inputVideo,
    const std::wstring& outputVideo,
    EngineType engine,
    bool twoPhase,
    ProgressCallback progressCb,
    void* userData
) {
    try {

        ImageMattingFactory::CreateAllTensorRtEngines(false);



        // Convert paths to strings
        std::string inputPath = WStringToString(inputVideo);
        std::string outputPath = WStringToString(outputVideo);

        if (inputPath.empty() || outputPath.empty()) {
            std::cerr << "Invalid input or output path" << std::endl;
            return 1;
        }

        // Initialize CUDA driver API
        cuInit(0);

        // Get device and create context explicitly
        CUdevice device;
        CUcontext context;
        cuDeviceGet(&device, 0);
        cuCtxCreate(&context, 0, device);

        // Make it current
        cuCtxSetCurrent(context);

        // Set FFmpeg log level to quiet to reduce overhead
        av_log_set_level(AV_LOG_QUIET);

        // Initialize TensorRT engines based on engine type
        bool useTensorRT = false;
        switch (engine) {
            case ENGINE_TENSORRT:
                useTensorRT = true;
                break;
            case ENGINE_AUTO:
                // Let ImageMattingFactory decide automatically
                useTensorRT = false; // Factory will auto-select
                break;
            case ENGINE_ONNX:
            default:
                useTensorRT = false;
                break;
        }

        // Create TensorRT engines if needed
        if (useTensorRT || engine == ENGINE_AUTO) {
            ImageMattingFactory::CreateAllTensorRtEngines(false);
        }

        int result = 0;

        // Choose processing method based on twoPhase parameter
        if (twoPhase) {
            std::cout << "Starting two-phase video background removal..." << std::endl;
            result = ProcessTwoPhase(inputPath, outputPath, context, progressCb, userData);
        } else {
            std::cout << "Starting one-phase video background removal..." << std::endl;
            result = ProcessOnePhase(inputPath, outputPath, context, progressCb, userData);
        }

        // Cleanup CUDA context
        cuCtxDestroy(context);

        if (result == 0) {
            std::cout << "Video background removal completed successfully!" << std::endl;
            std::cout << "Output saved to: " << outputPath << std::endl;
        } else {
            std::cout << "Video background removal failed!" << std::endl;
        }

        return result;
    }
    catch (const std::exception& e) {
        std::cerr << "Error in VideoBackgroundRemoval: " << e.what() << std::endl;
        return 1;
    }
}
