#include "FrameProcessor.h"
#include "DirectVideoReader.h"
#include "DirectVideoWriterAlphaCuda.h"
#include <cuda.h>
#include <string>
#include <memory>
#include <atomic>
#include <functional>

// Progress callback type (matches DLL)
typedef bool (__stdcall *ProgressCallback)(int currentFrame, int totalFrames, void* userData);

// Engine type enum (matches DLL)
enum EngineType {
    ENGINE_ONNX = 0,
    ENGINE_TENSORRT = 1,
    ENGINE_AUTO = 2
};

// Returns 0 on success, nonzero on error/cancel
int VideoBackgroundRemoval(
    const std::wstring& inputVideo,
    const std::wstring& outputVideo,
    EngineType engine,
    bool twoStep,
    ProgressCallback progressCb,
    void* userData
) {
    // TODO: Implement the shared logic for video background removal and ProRes4444 export
    // - Use engine type to select backend
    // - Use twoStep to select one-step or two-step processing
    // - Call progressCb(currentFrame, totalFrames, userData) periodically
    // - If progressCb returns false, cancel processing and return error code
    return -1; // Not implemented
}
