#pragma once

#ifdef IMAGEMATTERLIB_EXPORTS
#define IMAGEMATTERLIB_API __declspec(dllexport)
#else
#define IMAGEMATTERLIB_API __declspec(dllimport)
#endif

extern "C" {

// Engine type enum (matches C#)
enum EngineType {
    ENGINE_ONNX = 0,
    ENGINE_TENSORRT = 1,
    ENGINE_AUTO = 2
};

// Progress callback: returns true to continue, false to cancel
// currentFrame: 0-based index, totalFrames: total number of frames (if known, else 0)
typedef bool (__stdcall *ProgressCallback)(int currentFrame, int totalFrames, void* userData);

// Main function: returns 0 on success, nonzero on error/cancel
IMAGEMATTERLIB_API int ConvertVideoToProRes4444(
    const wchar_t* inputVideo,
    const wchar_t* outputVideo,
    EngineType engine,
    bool twoStep,
    ProgressCallback progressCb,
    void* userData
);

}
