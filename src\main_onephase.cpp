// CUDA ProRes 4444 Encoding with AI Alpha Matting
//
// This application processes video files to add AI-generated alpha channels
// and encodes them to ProRes 4444 format using GPU acceleration.
//

#include <Windows.h>
#include <iostream>
#include <string>
#include <memory>
#include <chrono>
#include <thread>
#include <queue>
#include <mutex>
#include <condition_variable>
#include <atomic>
#include <iomanip>
#include "cuda_runtime.h"
#include <cuda.h>  // CUDA Driver API
#include "DirectVideoWriterAlphaCuda.h"
#include "DirectVideoReader.h"
#include <Helpers.h>
#include <main_Kernels.cuh>
#include <FrameProcessor.h>
#include <CudaProResEncoder.h>
#include <ImageMattingFactory.h>

// CUDA ProRes 4444 encoding with AI alpha matting
bool TestCudaProResEncoding(CUcontext context) {
    std::cout << "\n=== CUDA ProRes 4444 Encoding (GPU Accelerated) ===" << std::endl;

    std::string inputPath = "Videos\\input short.mp4";  // Regular video
    std::string outputPath = "Videos\\output_cuda_prores.mov";

    try {
        // Create regular video reader (hardware accelerated for speed)
        auto reader = DirectVideoReader::Create(inputPath, context, true);
        if (!reader) {
            std::cerr << "Failed to create video reader for: " << inputPath << std::endl;
            return false;
        }

        std::cout << "Input video properties:" << std::endl;
        std::cout << "  Dimensions: " << reader->GetWidth() << "x" << reader->GetHeight() << std::endl;
        std::cout << "  Duration: " << reader->GetDuration() << " seconds" << std::endl;
        std::cout << "  Frame rate: " << reader->GetFrameRateDouble() << " fps" << std::endl;

        // Create CUDA stream for processing
        cudaStream_t processingStream;
        CUDA_CHECK(cudaStreamCreate(&processingStream));

        // Initialize frame processor
        auto processor = std::make_unique<FrameProcessor>();

        // Initialiser avec s�lection automatique (recommand�)
        if (!processor->Initialize(reader->GetWidth(), reader->GetHeight(), processingStream)) {
            std::cerr << "Failed to initialize FrameProcessor" << std::endl;
            return false;
        }
        std::cout << "FrameProcessor initialized successfully" << std::endl;

        // Configure output for CUDA ProRes 4444 with alpha
        DirectVideoWriterAlphaCuda::OutputConfig outputConfig;
        outputConfig.width = reader->GetWidth();
        outputConfig.height = reader->GetHeight();
        outputConfig.frameRate = reader->GetFrameRate();
        outputConfig.outputPath = outputPath;
        outputConfig.UseProRes4444();

        // Create CUDA alpha video writer
        auto writer = DirectVideoWriterAlphaCuda::Create(outputConfig, context);
        if (!writer) {
            std::cerr << "Failed to create CUDA ProRes video writer" << std::endl;
            return false;
        }

        // Allocate buffers for hardware-accelerated video reading (NV12 format)
        int nv12Height = reader->GetHeight() + reader->GetHeight() / 2;
        void* d_inputNv12Array = nullptr;
        size_t inputPitch = 0;
        CUDA_CHECK(cudaMallocPitch(&d_inputNv12Array, &inputPitch, reader->GetWidth(), nv12Height));

        // Allocate buffer for RGBA output (FrameProcessor outputs RGBA directly with estimated background)
        size_t rgbaBufferSize = reader->GetWidth() * reader->GetHeight() * 4 * sizeof(float);
        void* d_rgbaBuffer = nullptr;
        CUDA_CHECK(cudaMalloc(&d_rgbaBuffer, rgbaBufferSize));

        std::cout << "Processing frames with CUDA ProRes encoding and FrameProcessor..." << std::endl;
        int frameCount = 0;
        double timestamp = 0.0;
        auto startTime = std::chrono::high_resolution_clock::now();

        // Timing accumulators
        double totalReadTime = 0.0;
        double totalFrameProcessorTime = 0.0;
        double totalEncodingTime = 0.0;

        // Process all frames
        while (true) {
            auto frameStartTime = std::chrono::high_resolution_clock::now();

            // === TIMING GROUP 1: Video Frame Reading ===
            auto readStart = std::chrono::high_resolution_clock::now();
            timestamp = reader->ReadFrame(d_inputNv12Array, inputPitch * nv12Height, inputPitch);
            if (timestamp < 0.0) break;
            auto readEnd = std::chrono::high_resolution_clock::now();
            double readTime = std::chrono::duration<double, std::milli>(readEnd - readStart).count();
            totalReadTime += readTime;

            // === TIMING GROUP 2: FrameProcessor (AI Alpha Matting + Background Estimation) ===
            auto processorStart = std::chrono::high_resolution_clock::now();
            if (!processor->ProcessFrame(d_inputNv12Array, inputPitch, static_cast<float*>(d_rgbaBuffer))) {
                std::cerr << "Failed to process frame " << frameCount << " with FrameProcessor" << std::endl;
                break;
            }
            auto processorEnd = std::chrono::high_resolution_clock::now();
            double processorTime = std::chrono::duration<double, std::milli>(processorEnd - processorStart).count();
            totalFrameProcessorTime += processorTime;

            // === TIMING GROUP 3: ProRes Encoding ===
            auto encodingStart = std::chrono::high_resolution_clock::now();
            if (!writer->WriteFrame(d_rgbaBuffer, rgbaBufferSize)) {
                std::cerr << "Failed to write frame " << frameCount << std::endl;
                break;
            }
            auto encodingEnd = std::chrono::high_resolution_clock::now();
            double encodingTime = std::chrono::duration<double, std::milli>(encodingEnd - encodingStart).count();
            totalEncodingTime += encodingTime;

            frameCount++;

            // Detailed timing output every 10 frames
            if (frameCount % 10 == 0) {
                auto frameEndTime = std::chrono::high_resolution_clock::now();
                double totalFrameTime = std::chrono::duration<double, std::milli>(frameEndTime - frameStartTime).count();

                std::cout << "\n=== Frame " << frameCount << " Timing (timestamp: " << std::fixed << std::setprecision(3) << timestamp << "s) ===" << std::endl;
                std::cout << "  Total frame time: " << std::fixed << std::setprecision(1) << totalFrameTime << " ms" << std::endl;
                std::cout << "  - Video reading: " << std::fixed << std::setprecision(1) << readTime << " ms" << std::endl;
                std::cout << "  - FrameProcessor: " << std::fixed << std::setprecision(1) << processorTime << " ms" << std::endl;
                std::cout << "  - ProRes encoding: " << std::fixed << std::setprecision(1) << encodingTime << " ms" << std::endl;
                std::cout << "Encoding queue size: " << writer->GetQueueSize() << "/" << 10 << std::endl;

                // Average timings so far
                std::cout << "\n--- Average Timings (last " << frameCount << " frames) ---" << std::endl;
                std::cout << "  Avg video reading: " << std::fixed << std::setprecision(1) << (totalReadTime / frameCount) << " ms/frame" << std::endl;
                std::cout << "  Avg FrameProcessor (AI + Background): " << std::fixed << std::setprecision(1) << (totalFrameProcessorTime / frameCount) << " ms/frame" << std::endl;
                std::cout << "  Avg ProRes encoding: " << std::fixed << std::setprecision(1) << (totalEncodingTime / frameCount) << " ms/frame" << std::endl;
                std::cout << "  Total avg per frame: " << std::fixed << std::setprecision(1) << ((totalReadTime + totalFrameProcessorTime + totalEncodingTime) / frameCount) << " ms/frame" << std::endl;
            }
        }

        auto endTime = std::chrono::high_resolution_clock::now();
        auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(endTime - startTime).count() / 1000.0;

        std::cout << "Finalizing CUDA ProRes video..." << std::endl;
        writer->Finalize();

        // Clean up frame processor
        processor->Cleanup();

        // Cleanup - use cudaFree for cudaMallocPitch allocated memory
        if (d_inputNv12Array) cudaFree(d_inputNv12Array);
        if (d_rgbaBuffer) cudaFree(d_rgbaBuffer);

        // Cleanup processing stream
        if (processingStream) cudaStreamDestroy(processingStream);

        // Final timing summary
        std::cout << "\n=== FINAL TIMING SUMMARY ===" << std::endl;
        std::cout << "Total processing time: " << std::fixed << std::setprecision(2) << duration << " seconds" << std::endl;
        std::cout << "Total frames processed: " << frameCount << std::endl;
        std::cout << "Overall average speed: " << std::fixed << std::setprecision(1) << (frameCount / duration) << " fps" << std::endl;
        std::cout << "\nAverage time per operation:" << std::endl;
        std::cout << "  Video reading: " << std::fixed << std::setprecision(1) << (totalReadTime / frameCount) << " ms/frame" << std::endl;
        std::cout << "  FrameProcessor (AI + Background): " << std::fixed << std::setprecision(1) << (totalFrameProcessorTime / frameCount) << " ms/frame" << std::endl;
        std::cout << "  ProRes encoding: " << std::fixed << std::setprecision(1) << (totalEncodingTime / frameCount) << " ms/frame" << std::endl;
        std::cout << "\nTime distribution:" << std::endl;
        double totalOperationTime = totalReadTime + totalFrameProcessorTime + totalEncodingTime;
        std::cout << "  Video reading: " << std::fixed << std::setprecision(1) << (totalReadTime / totalOperationTime * 100) << "%" << std::endl;
        std::cout << "  FrameProcessor: " << std::fixed << std::setprecision(1) << (totalFrameProcessorTime / totalOperationTime * 100) << "%" << std::endl;
        std::cout << "  ProRes encoding: " << std::fixed << std::setprecision(1) << (totalEncodingTime / totalOperationTime * 100) << "%" << std::endl;

        std::cout << "\nFinal stats: " << writer->GetFrameCount() << " frames encoded, " << writer->GetTotalBytes() << " bytes" << std::endl;
        std::cout << "Output saved to: " << outputPath << std::endl;
        return true;
    }
    catch (const std::exception& e) {
        std::cerr << "Error creating CUDA ProRes video: " << e.what() << std::endl;
        return false;
    }
}

int main(int argc, char* argv[]) {

    ImageMattingFactory::CreateAllTensorRtEngines(false);

    std::cout << "=== CUDA ProRes 4444 Encoding with AI Alpha Matting ===" << std::endl;

    // Initialize CUDA driver API
    cuInit(0);

    // Get device and create context explicitly
    CUdevice device;
    CUcontext context;
    cuDeviceGet(&device, 0);
    cuCtxCreate(&context, 0, device);

    // Make it current
    cuCtxSetCurrent(context);

    // Set FFmpeg log level to quiet to reduce overhead
    av_log_set_level(AV_LOG_QUIET);

    try {
        bool success = TestCudaProResEncoding(context);

        if (success) {
            std::cout << "\n=== Processing completed successfully! ===" << std::endl;
        } else {
            std::cout << "\n=== Processing failed! ===" << std::endl;
            return 1;
        }
    }
    catch (const std::exception& e) {
        std::cerr << "Error: " << e.what() << std::endl;
        return 1;
    }

    // Cleanup CUDA context
    cuCtxDestroy(context);

    return 0;
}
