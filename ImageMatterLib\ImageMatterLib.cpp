#include "ImageMatterLib.h"
#include <string>

// Declare the shared function
int VideoBackgroundRemoval(
    const std::wstring& inputVideo,
    const std::wstring& outputVideo,
    EngineType engine,
    bool twoStep,
    ProgressCallback progressCb,
    void* userData
);

extern "C" {

int ConvertVideoToProRes4444(
    const wchar_t* inputVideo,
    const wchar_t* outputVideo,
    EngineType engine,
    bool twoStep,
    ProgressCallback progressCb,
    void* userData
) {
    // Convert wchar_t* to std::wstring
    std::wstring in(inputVideo ? inputVideo : L"");
    std::wstring out(outputVideo ? outputVideo : L"");
    return VideoBackgroundRemoval(in, out, engine, twoStep, progressCb, userData);
}

}
