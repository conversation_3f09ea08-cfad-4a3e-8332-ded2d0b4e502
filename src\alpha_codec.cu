#include "alpha_codec.cuh"
#include "alpha_codec.h"
#include <cuda_runtime.h>
#include <device_launch_parameters.h>
#include <cstdint>
#include <algorithm>

#ifdef __CUDACC__

// CUDA kernel implementations
__device__ void write_repeat_count_device(int* output, int& pos, int width, int x, int count, int previous) {
	if (count > 255) {
		output[pos * width + x] = REPEAT_LONG;
		pos++;
		output[pos * width + x] = count / 256;
		pos++;
		output[pos * width + x] = count % 256;
		pos++;
	}
	else if (count > 2) {
		output[pos * width + x] = REPEAT_SHORT;
		pos++;
		output[pos * width + x] = count;
		pos++;
	}
	else if (count <= 2) {
		// Write individual values
		for (int c = 0; c < count; c++) {
			output[pos * width + x] = previous;
			pos++;
		}
	}
}

__global__ void compress_columns_kernel(const uint8_t* input,
	int width,
	int height,
	int* column_headers,
	int* output) {
	int x = blockIdx.x * blockDim.x + threadIdx.x;
	if (x >= width)
		return;

	int pos = 0;
	int count = 0;
	int value = 0;
	int previous = -1;

	for (int y = 0; y < height; y++) {
		value = input[y * width + x];

		// Handle special values
		if (value == REPEAT_SHORT)
			value = 0;
		if (value == REPEAT_LONG)
			value = 255;

		if (value == previous) {
			count++;
		}
		else {
			if (count > 0) {
				if (count > 255) {
					output[pos * width + x] = REPEAT_LONG;
					pos++;
					output[pos * width + x] = count / 256;
					pos++;
					output[pos * width + x] = count % 256;
					pos++;
				}
				else if (count > 2) {
					output[pos * width + x] = REPEAT_SHORT;
					pos++;
					output[pos * width + x] = count;
					pos++;
				}
				else {
					for (int c = 0; c < count; c++) {
						output[pos * width + x] = previous;
						pos++;
					}
				}
			}

			count = 1;
			output[pos * width + x] = value;
			pos++;
			previous = value;
		}
	}

	// Handle remaining count
	if (count > 1) {
		if (count > 255) {
			output[pos * width + x] = REPEAT_LONG;
			pos++;
			output[pos * width + x] = count / 256;
			pos++;
			output[pos * width + x] = count % 256;
			pos++;
		}
		else if (count > 2) {
			output[pos * width + x] = REPEAT_SHORT;
			pos++;
			output[pos * width + x] = count;
			pos++;
		}
		else {
			for (int c = 1; c < count; c++) {
				output[pos * width + x] = previous;
				pos++;
			}
		}
	}

	column_headers[x] = pos;
}

__global__ void add_column_headers_kernel(int* column_headers, int width) {
	// Single thread kernel
	if (threadIdx.x == 0 && blockIdx.x == 0) {
		int index_compressed = 0;
		for (int x = 0; x < width; x++) {
			index_compressed += column_headers[x];
			column_headers[x] = index_compressed;
		}
	}
}

__global__ void compress_lines_kernel(const int* column_headers,
	const int* input,
	int* output,
	int width) {
	int x = blockIdx.x * blockDim.x + threadIdx.x;
	if (x >= width)
		return;

	int start_index_compressed = (x == 0) ? 0 : column_headers[x - 1];
	int length = column_headers[x] - start_index_compressed;

	for (int pos = 0; pos < length; pos++) {
		int index_input = pos * width + x;
		int value = input[index_input];
		output[start_index_compressed + pos] = value;
	}
}

__global__ void decompress_kernel(const int* column_headers,
	const int* input,
	uint8_t* output,
	int width,
	int height) {
	int x = blockIdx.x * blockDim.x + threadIdx.x;
	if (x >= width)
		return;

	int y = 0;
	int previous = 0;
	int index_compressed = (x == 0) ? 0 : column_headers[x - 1];

	while (index_compressed < column_headers[x] && y < height) {
		int value = input[index_compressed++];

		if (value == REPEAT_SHORT) {
			int count = input[index_compressed++];
			for (int i = 0; i < count && y < height; i++) {
				output[y * width + x] = previous;
				y++;
			}
		}
		else if (value == REPEAT_LONG) {
			int count = input[index_compressed++] * 256 + input[index_compressed++];
			for (int i = 0; i < count && y < height; i++) {
				output[y * width + x] = previous;
				y++;
			}
		}
		else {
			if (y < height) {
				output[y * width + x] = value;
				y++;
			}
		}
		previous = value;
	}

	// Fill remaining pixels with previous value
	while (y < height) {
		output[y * width + x] = previous;
		y++;
	}
}

// Kernel launch functions with extern "C" linkage
extern "C" {
void launch_compress_columns(const uint8_t* d_input,
	int width,
	int height,
	int* d_column_headers,
	int* d_output,
	cudaStream_t stream) {
	int block_size = 256;
	int grid_size = (width + block_size - 1) / block_size;

	compress_columns_kernel<<<grid_size, block_size, 0, stream>>>(
		d_input, width, height, d_column_headers, d_output);
}

void launch_add_column_headers(int* d_column_headers,
	int width,
	cudaStream_t stream) {
	add_column_headers_kernel<<<1, 1, 0, stream>>>(d_column_headers, width);
}

void launch_compress_lines(const int* d_column_headers,
	const int* d_input,
	int* d_output,
	int width,
	cudaStream_t stream) {
	int block_size = 256;
	int grid_size = (width + block_size - 1) / block_size;

	compress_lines_kernel<<<grid_size, block_size, 0, stream>>>(
		d_column_headers, d_input, d_output, width);
}

void launch_decompress(const int* d_column_headers,
	const int* d_input,
	uint8_t* d_output,
	int width,
	int height,
	cudaStream_t stream) {
	int block_size = 256;
	int grid_size = (width + block_size - 1) / block_size;

	decompress_kernel<<<grid_size, block_size, 0, stream>>>(
		d_column_headers, d_input, d_output, width, height);
}
}

#endif