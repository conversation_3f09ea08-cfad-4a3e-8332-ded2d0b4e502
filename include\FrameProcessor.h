#pragma once

#include <vector>
#include <future>
#include <memory>
#include "cuda_runtime.h"
#include "Helpers_Heads.h"
#include "HeadDetector.h"
#include "ImageMatting.h"
#include "BackgroundEstimationKernels.cuh"
#include "alpha_codec.h"

class FrameProcessor {
private:

    // CUDA streams
    cudaStream_t processingStream;
    cudaStream_t bodyStream;
    cudaStream_t headStream;

    // Device buffers
    unsigned char* d_regionsBufferBody;
    unsigned char* d_regionsBufferHair;
    float* d_trimapBuffer;
    int* d_edgePositions;
    Box* d_headBoxes;
    cudaArray_t d_alphaArray;
    cudaTextureObject_t d_originalAlphaTexture;

    // Background estimation buffers
    void* d_horizontalBgData;    // Horizontal background estimates
    void* d_verticalBgData;      // Vertical background estimates
    float* d_finalRgbaData;      // Final RGBA output with estimated background

    // Host buffers
    unsigned char* h_regionsBufferBody;
    unsigned char* h_regionsBufferHair;

    // Buffer sizes
    size_t regionsBufferSizeBody;
    size_t regionsBufferSizeHair;

    // Processing parameters
    int videoWidth;
    int videoHeight;
    int regionSizeBody;
    int regionSizeHair;
    int IndexNetModelInputSize;
    int MaxUncertainRegionWidthForBody;
    int MaxUncertainRegionWidthForHair;

    int NumVerticalRegionCountBody;
    int NumVerticalRegionCountHair;


    // Head detection
    std::unique_ptr<HeadDetector> headDetector;
    std::unique_ptr<ImageMatting> initialImageMatter;
    std::unique_ptr<ImageMatting> indexNetImageMatter;
    std::vector<Box> headDetectionResults;

    // Private processing functions
    void ProcessBodyRegions();
    void ProcessHeadRegions();
    bool AllocateBuffers();
    void DeallocateBuffers();

public:
    FrameProcessor();
    ~FrameProcessor();

    // Initialize the processor with video parameters
    bool Initialize(int width, int height, cudaStream_t mainStream);

    // Initialize only the initial matting components (for two-phase processing)
    bool InitializeInitialMatte(int width, int height, cudaStream_t mainStream);

    // Initialize only the refinement components (for two-phase processing)
    bool InitializeRefinement(int width, int height, cudaStream_t mainStream);

    // Process a single frame and output RGBA with estimated background
    bool ProcessFrame(void* inputNv12Data, size_t inputPitch, float* outputRgbaData);

    // Find initial alpha matte and encode it (for two-phase processing)
    bool FindInitialAlpha(void* inputNv12Data, size_t inputPitch, unsigned char** encodedAlpha, size_t* encodedSize);

    // Refine initial alpha and complete processing (for two-phase processing)
    bool RefineInitialAlpha(void* inputNv12Data, size_t inputPitch, unsigned char* encodedAlpha, size_t encodedSize, float* outputRgbaData);
    
    // Cleanup resources
    void Cleanup();
};
